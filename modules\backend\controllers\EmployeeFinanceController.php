<?php
namespace app\modules\backend\controllers;

use app\common\models\CashboxBalanceHistory;
use app\common\models\CashboxBalanceInouts;
use app\common\models\ExpenseHistory;
use Yii;
use yii\web\Response;
use app\common\models\Expenses;
use app\common\models\Cashbox;
use app\common\models\CashboxBalance;
use app\common\models\Employee;
use app\common\models\EmployeeFinances;
use app\common\models\EmployeeSalary;
use app\common\models\EmployeeDebt;
use app\common\models\EmployeeDebtRepayment;

class EmployeeFinanceController extends BaseController
{
    public function actionIndex()
    {
        $sql = "WITH RunningTotal AS (
            SELECT
                wf.id,
                wf.employee_id,
                wf.month,
                wf.amount,
                wf.debt_deduction_amount,
                wf.net_amount,
                wf.type,
                wf.description,
                wf.created_at,
                e.full_name as employee_name,
                p.name as position,
                ws.amount as salary,
                SUM(
                    CASE
                       WHEN wf.type = :type_1 OR wf.type = :type_2 THEN wf.amount
                        ELSE 0
                    END
                ) OVER (
                    PARTITION BY wf.employee_id, wf.month
                    ORDER BY wf.created_at
                    ROWS BETWEEN UNBOUNDED PRECEDING AND CURRENT ROW
                ) as total_paid
            FROM employee_finances wf
            LEFT JOIN employee e ON e.id = wf.employee_id
            LEFT JOIN position p ON p.id = e.position_id
            LEFT JOIN employee_salary ws ON ws.employee_id = e.id AND ws.end_date = '9999-12-31'
            WHERE wf.deleted_at IS NULL
        )
        SELECT
            *,
            GREATEST(0, salary - total_paid) as remaining_salary
        FROM RunningTotal
        ORDER BY created_at DESC";

        $command = Yii::$app->db->createCommand($sql);
        $command->bindValue(':type_1', EmployeeFinances::TYPE_SALARY);
        $command->bindValue(':type_2', EmployeeFinances::TYPE_ADVANCE);
        $result = $command->queryAll();

        return $this->render('index', [
            'result' => $result
        ]);
    }

    public function actionSearch()
    {
        Yii::$app->response->format = Response::FORMAT_JSON;

        $start_date = Yii::$app->request->post('start_date');
        $end_date = Yii::$app->request->post('end_date');

        $sql = "WITH RunningTotal AS (
            SELECT
                wf.id,
                wf.employee_id,
                wf.month,
                wf.amount,
                wf.debt_deduction_amount,
                wf.net_amount,
                wf.type,
                wf.description,
                wf.created_at,
                e.full_name as employee_name,
                p.name as position,
                ws.amount as salary,
                SUM(
                    CASE
                        WHEN wf.type = :type_1 OR wf.type = :type_2 THEN wf.amount
                        ELSE 0
                    END
                ) OVER (
                    PARTITION BY wf.employee_id, wf.month
                    ORDER BY wf.created_at
                    ROWS BETWEEN UNBOUNDED PRECEDING AND CURRENT ROW
                ) as total_paid
            FROM employee_finances wf
            LEFT JOIN employee e ON e.id = wf.employee_id
            LEFT JOIN position p ON p.id = e.position_id
            LEFT JOIN employee_salary ws ON ws.employee_id = e.id AND ws.end_date = '9999-12-31'
            WHERE wf.deleted_at IS NULL
        )
        SELECT
            *,
            GREATEST(0, salary - total_paid) as remaining_salary
        FROM RunningTotal
        WHERE 1=1";

        // Добавляем фильтрацию по датам, если они переданы
        if ($start_date) {
            $sql .= " AND DATE(created_at) >= :start_date";
        }
        if ($end_date) {
            $sql .= " AND DATE(created_at) <= :end_date";
        }

        $sql .= " ORDER BY created_at DESC";

        $command = Yii::$app->db->createCommand($sql);
        $command->bindValue(':type_1', EmployeeFinances::TYPE_SALARY);
        $command->bindValue(':type_2', EmployeeFinances::TYPE_ADVANCE);

        if ($start_date) {
            $command->bindValue(':start_date', $start_date);
        }
        if ($end_date) {
            $command->bindValue(':end_date', $end_date);
        }

        try {
            $result = $command->queryAll();

            return [
                'status' => 'success',
                'html' => $this->renderPartial('_index', [
                    'model' => $result // Передаем результат в представление
                ])
            ];
        } catch (\Exception $e) {
            return [
                'status' => 'error',
                'message' => $e->getMessage()
            ];
        }
    }

    public function actionCheckSalary()
    {
        Yii::$app->response->format = Response::FORMAT_JSON;
        $employeeId = Yii::$app->request->get('employee_id');

        if (!$employeeId) {
            return [
                'status' => 'error',
                'message' => 'Ходим танланмаган!',
            ];
        }

        $currentSalary = EmployeeSalary::find()
            ->where(['employee_id' => $employeeId])
            ->andWhere(['end_date' => '9999-12-31'])
            ->andWhere(['deleted_at' => null])
            ->one();

        if ($currentSalary) {
            return [
                'status' => 'success',
                'has_salary' => true,
                'amount' => $currentSalary->amount
            ];
        } else {
            return [
                'status' => 'success',
                'has_salary' => false,
            ];
        }
    }

    private function findAvailableMonth($employee_id, $employeeSalary, $month)
    {
        $query = EmployeeFinances::find()
            ->where([
                'employee_id' => $employee_id,
                'month' => $month,
                'deleted_at' => null,
            ])
            ->andWhere(['<>', 'type', EmployeeFinances::TYPE_BONUS]);

        $totalPaid = $query->sum('amount') ?: 0;
        $remaining = $employeeSalary->amount - $totalPaid;

        // Если в текущем месяце уже нет остатка, ищем следующий доступный месяц
        if ($remaining <= 0) {
            return $this->findNextAvailableMonth($employee_id, $employeeSalary, $month);
        }

        return [
            'month' => $month,
            'remaining' => $remaining,
            'max_amount' => $employeeSalary->amount
        ];
    }

    private function findNextAvailableMonth($employee_id, $employeeSalary, $currentMonth)
    {
        $date = new \DateTime($currentMonth . '-01');

        for ($i = 0; $i < 12; $i++) {
            $date->modify('+1 month');
            $nextMonth = $date->format('Y-m');

            $query = EmployeeFinances::find()
                ->where([
                    'employee_id' => $employee_id,
                    'month' => $nextMonth,
                    'deleted_at' => null,
                ])
                ->andWhere(['<>', 'type', EmployeeFinances::TYPE_BONUS]);

            $totalPaid = $query->sum('amount') ?: 0;
            $remaining = $employeeSalary->amount - $totalPaid;

            if ($remaining > 0) {
                return [
                    'month' => $nextMonth,
                    'remaining' => $remaining,
                    'max_amount' => $employeeSalary->amount
                ];
            }
        }

        // Если не найдено ни одного месяца с остатком
        return [
            'month' => $currentMonth,
            'remaining' => 0,
            'max_amount' => $employeeSalary->amount
        ];
    }

    public function actionGetLastPaymentMonth()
    {
        Yii::$app->response->format = Response::FORMAT_JSON;

        try {
            $employee_id = Yii::$app->request->get('employee_id');

            if (!$employee_id) {
                throw new \Exception('Ходим танланмаган!');
            }

            $employeeSalary = EmployeeSalary::find()
                ->where([
                    'employee_id' => $employee_id,
                    'end_date' => '9999-12-31',
                    'deleted_at' => null
                ])
                ->one();

            if (!$employeeSalary) {
                throw new \Exception('Ходимнинг актив маоши топилмади!');
            }

            $currentMonth = date('Y-m');
            $result = $this->findAvailableMonth($employee_id, $employeeSalary, $currentMonth);

            return [
                'status' => 'success',
                'month' => $result['month'],
                'remaining' => $result['remaining'],
                'max_amount' => $result['max_amount'] ?? null
            ];

        } catch (\Exception $e) {
            return [
                'status' => 'error',
                'message' => $e->getMessage()
            ];
        }
    }

    public function actionCreate()
    {
        Yii::$app->response->format = Response::FORMAT_JSON;

        if (Yii::$app->request->isGet) {
        $model = new EmployeeFinances();
            $workers = Employee::find()
                ->select(['id', 'full_name'])
                ->where(['deleted_at' => null])
                ->asArray()
                ->all();

            $cashboxes = Cashbox::find()
                ->where(['deleted_at' => null])
                ->all();

                $cashboxData = [];
                foreach ($cashboxes as $cashbox) {
                    $balances = CashboxBalance::find()
                        ->select(['cb.*', 'c.name as currency_name'])
                        ->alias('cb')
                        ->leftJoin(['c' => 'currency'], 'c.id = cb.currency_id')
                        ->where(['cb.cashbox_id' => $cashbox->id])
                        ->andWhere(['cb.deleted_at' => null])
                        ->andWhere(['>', 'cb.sum', 0])
                        ->andWhere(['!=', 'cb.currency_id', 1])
                        ->asArray()
                        ->all();

                    if (!empty($balances)) {
                        $cashboxData[] = [
                            'id' => $cashbox->id,
                            'name' => $cashbox->name,
                            'balances' => $balances
                        ];
                    }
                }

            return [
                'status' => true,
                'content' => $this->renderPartial('create', [
                    'model' => $model,
                    'workers' => $workers,
                    'cashboxData' => $cashboxData
                ])
            ];
        }

        if (!Yii::$app->request->isPost) {
            return [
                'status' => 'error',
                'message' => 'Неверный метод запроса',
            ];
        }

        $model = new EmployeeFinances();
        $model->load(Yii::$app->request->post());
        $model->user_id = Yii::$app->user->id;

        // Получаем данные об удержаниях с долгов
        $deductDebt = Yii::$app->request->post('deduct_debt', false);
        $deductionAmount = (float) Yii::$app->request->post('deduction_amount', 0);

        // Устанавливаем значения по умолчанию для новых полей
        $model->debt_deduction_amount = $deductDebt ? $deductionAmount : 0;
        $model->net_amount = $model->amount - $model->debt_deduction_amount;

        // Дополнительная валидация
        if ($deductDebt && $deductionAmount <= 0) {
            return [
                'status' => 'error',
                'message' => 'Сумма удержания должна быть больше 0',
            ];
        }

        if ($model->debt_deduction_amount > $model->amount) {
            return [
                'status' => 'error',
                'message' => 'Сумма удержания (' . number_format($model->debt_deduction_amount, 2) . ') не может превышать сумму выплаты (' . number_format($model->amount, 2) . '). Пожалуйста, скорректируйте сумму удержания.',
            ];
        }

        // Проверяем, что итоговая сумма к выплате не отрицательная
        if ($model->net_amount < 0) {
            return [
                'status' => 'error',
                'message' => 'Итоговая сумма к выплате не может быть отрицательной. Сумма удержания слишком большая.',
            ];
        }

        if (!$model->validate()) {
            return [
                'success' => 'error',
                'errors' => $model->getErrors(),
            ];
        }

            $transaction = Yii::$app->db->beginTransaction();
            try {
            $employeeSalary = EmployeeSalary::find()
                ->where([
                    'employee_id' => $model->employee_id,
                    'end_date' => '9999-12-31',
                    'deleted_at' => null
                ])
                ->one();

            // Если нет зарплаты и пытаемся платить не бонус - выдаем ошибку
            if (!$employeeSalary && $model->type != EmployeeFinances::TYPE_BONUS) {
                throw new \Exception('Для выплаты зарплаты или аванса необходимо установить зарплату сотруднику');
            }

            // Сначала сохраняем основную запись EmployeeFinances
            if (!$model->save()) {
                throw new \Exception('Не удалось сохранить запись о выплате: ' . json_encode($model->getErrors(), JSON_UNESCAPED_UNICODE));
            }

            // ВАЖНО: Сохраняем оригинальную сумму ДО обработки разделения на месяцы
            $originalNetAmount = $model->net_amount;

            // Обрабатываем разные типы выплат
            if ($model->type == EmployeeFinances::TYPE_BONUS) {
                // Для бонусов используем старую логику с createPaymentRecord
                if (!$this->createPaymentRecord($model, $model->amount, $model->month, $employeeSalary)) {
                    throw new \Exception('Не удалось создать запись о платеже');
                }
            } else if ($employeeSalary) {
                // Для зарплат и авансов проверяем, нужно ли разделение на месяцы
                $this->processSalaryPayment($model, $employeeSalary);
            }

            // Обрабатываем удержания с долгов, если они есть (ПЕРЕД обновлением баланса кассы)
            if ($deductDebt && $deductionAmount > 0) {
                $this->processDebtRepayments($model->employee_id, $model->currency_id, $deductionAmount, $model->id);
            }

            $cashboxBalance = CashboxBalance::find()
                ->where(['cashbox_id' => $model->cashbox_id, 'currency_id' => $model->currency_id])
                ->one();

            if (!$cashboxBalance) {
                throw new \Exception('Баланс кассы не найден');
            }

            if ($cashboxBalance->sum < $originalNetAmount) {
                throw new \Exception('[INSUFFICIENT_BALANCE] ' . Yii::t('app', 'insufficient_balance'));
            }

            $cashboxBalance->sum -= $originalNetAmount;
            if (!$cashboxBalance->save()) {
                throw new \Exception('Не удалось обновить баланс кассы');
            }

            $this->createCashboxBalanceRecords(
                $cashboxBalance->id,
                $model->cashbox_id,
                $model->currency_id,
                $originalNetAmount,
                'Ходим учун тўлов'
            );

            // Логируем создание выплаты сотруднику
            \app\common\models\ActionLog::logAction(
                Yii::$app->user->id,
                \app\common\models\ActionLog::TYPE_EMPLOYEE_PAYMENT,
                null,
                json_encode([
                    'employee_id' => $model->employee_id,
                    'amount' => $model->amount,
                    'net_amount' => $model->net_amount,
                    'debt_deduction_amount' => $model->debt_deduction_amount,
                    'currency_id' => $model->currency_id,
                    'type' => $model->type,
                    'month' => $model->month,
                    'cashbox_id' => $model->cashbox_id,
                    'description' => $model->description,
                    'created_at' => date('Y-m-d H:i:s')
                ])
            );

            $transaction->commit();
            return [
                'status' => 'success',
                'message' => 'Тўлов муваффақиятли сақланди',
            ];

            } catch (\Exception $e) {
                $transaction->rollBack();
                return [
                    'status' => 'error',
                    'message' => $e->getMessage(),
                ];
            }
        }

    /**
     * Обработка выплат зарплаты и авансов с проверкой разделения на месяцы
     */
    private function processSalaryPayment($model, $employeeSalary)
    {
        // Проверяем, сколько уже выплачено в текущем месяце (исключая основную запись)
        $totalPaid = EmployeeFinances::find()
            ->where([
                'employee_id' => $model->employee_id,
                'month' => $model->month,
                'deleted_at' => null,
            ])
            ->andWhere(['<>', 'type', EmployeeFinances::TYPE_BONUS])
            ->andWhere(['<>', 'id', $model->id]) // Исключаем текущую запись
            ->sum('amount');

        $remaining = $employeeSalary->amount - ($totalPaid ?: 0);

        if ($remaining <= 0) {
            // Текущий месяц полностью заполнен - переносим всю сумму на следующий месяц
            $this->movePaymentToNextMonth($model, $employeeSalary);
        } else if ($model->amount <= $remaining) {
            // Сумма помещается в текущий месяц - создаем только expense_history
            $this->createExpenseHistoryForSalary($model);
        } else {
            // Требуется разделение на месяцы
            $this->splitSalaryPaymentAcrossMonths($model, $remaining, $employeeSalary);
        }
    }

    /**
     * Перенос выплаты на следующий месяц (когда текущий месяц полностью заполнен)
     */
    private function movePaymentToNextMonth($originalModel, $employeeSalary)
    {
        // ВАЖНО: Сохраняем первоначальную сумму ДО изменения модели
        $originalAmount = $originalModel->amount;

        // Удаляем основную запись, так как она не нужна в текущем месяце
        $originalModel->deleted_at = date('Y-m-d H:i:s');
        if (!$originalModel->save()) {
            throw new \Exception('Не удалось удалить основную запись: ' . json_encode($originalModel->getErrors(), JSON_UNESCAPED_UNICODE));
        }

        // Создаем записи для следующих месяцев
        $remainingAmount = $originalAmount;
        $currentMonth = $originalModel->month;

        while ($remainingAmount > 0) {
            $nextMonth = date('Y-m', strtotime($currentMonth . '-01 +1 month'));

            // Проверяем, сколько можем выплатить в следующем месяце
            $nextMonthPaid = EmployeeFinances::find()
                ->where([
                    'employee_id' => $originalModel->employee_id,
                    'month' => $nextMonth,
                    'deleted_at' => null,
                ])
                ->andWhere(['<>', 'type', EmployeeFinances::TYPE_BONUS])
                ->sum('amount');

            $nextMonthRemaining = $employeeSalary->amount - ($nextMonthPaid ?: 0);
            $amountForNextMonth = min($remainingAmount, $nextMonthRemaining);

            if ($amountForNextMonth <= 0) {
                // Если в следующем месяце нет места, переходим к следующему
                $currentMonth = $nextMonth;
                continue;
            }

            // Создаем новую запись для следующего месяца
            $nextMonthPayment = new EmployeeFinances();
            $nextMonthPayment->employee_id = $originalModel->employee_id;
            $nextMonthPayment->user_id = $originalModel->user_id;
            $nextMonthPayment->amount = $amountForNextMonth;
            $nextMonthPayment->currency_id = $originalModel->currency_id;
            $nextMonthPayment->month = $nextMonth;
            $nextMonthPayment->type = $originalModel->type;
            $nextMonthPayment->cashbox_id = $originalModel->cashbox_id;
            $nextMonthPayment->description = $originalModel->description . ' (перенос с ' . $originalModel->month . ')';

            // Переносим удержания долгов только на первую запись
            if ($remainingAmount == $originalAmount) {
                $nextMonthPayment->debt_deduction_amount = $originalModel->debt_deduction_amount;
                $nextMonthPayment->net_amount = $amountForNextMonth - $originalModel->debt_deduction_amount;
            } else {
                $nextMonthPayment->debt_deduction_amount = 0;
                $nextMonthPayment->net_amount = $amountForNextMonth;
            }

            $nextMonthPayment->created_at = date('Y-m-d H:i:s');

            if (!$nextMonthPayment->save()) {
                throw new \Exception('Не удалось создать запись для месяца ' . $nextMonth . ': ' . json_encode($nextMonthPayment->getErrors(), JSON_UNESCAPED_UNICODE));
            }

            // Создаем expense_history для новой записи
            $this->createExpenseHistoryForSalary($nextMonthPayment);

            $remainingAmount -= $amountForNextMonth;
            $currentMonth = $nextMonth;
        }
    }

    /**
     * Разделение выплаты зарплаты на несколько месяцев
     */
    private function splitSalaryPaymentAcrossMonths($originalModel, $remainingInCurrentMonth, $employeeSalary)
    {
        // ВАЖНО: Сохраняем первоначальную сумму ДО изменения модели
        $originalAmount = $originalModel->amount;

        // 1. Обновляем основную запись для текущего месяца
        $originalModel->amount = $remainingInCurrentMonth;
        $originalModel->net_amount = $remainingInCurrentMonth - $originalModel->debt_deduction_amount;

        if (!$originalModel->save()) {
            throw new \Exception('Не удалось обновить основную запись: ' . json_encode($originalModel->getErrors(), JSON_UNESCAPED_UNICODE));
        }

        // Создаем expense_history для основной записи
        $this->createExpenseHistoryForSalary($originalModel);

        // 2. Создаем дополнительные записи для следующих месяцев
        $remainingAmount = $originalAmount - $remainingInCurrentMonth;
        $currentMonth = $originalModel->month;

        while ($remainingAmount > 0) {
            $nextMonth = date('Y-m', strtotime($currentMonth . '-01 +1 month'));

            // Проверяем, сколько можем выплатить в следующем месяце
            $nextMonthPaid = EmployeeFinances::find()
                ->where([
                    'employee_id' => $originalModel->employee_id,
                    'month' => $nextMonth,
                    'deleted_at' => null,
                ])
                ->andWhere(['<>', 'type', EmployeeFinances::TYPE_BONUS])
                ->sum('amount');

            $nextMonthRemaining = $employeeSalary->amount - ($nextMonthPaid ?: 0);
            $amountForNextMonth = min($remainingAmount, $nextMonthRemaining);

            if ($amountForNextMonth <= 0) {
                // Если в следующем месяце нет места, переходим к следующему
                $currentMonth = $nextMonth;
                continue;
            }

            // Создаем новую запись для следующего месяца
            $nextMonthPayment = new EmployeeFinances();
            $nextMonthPayment->employee_id = $originalModel->employee_id;
            $nextMonthPayment->user_id = $originalModel->user_id;
            $nextMonthPayment->amount = $amountForNextMonth;
            $nextMonthPayment->currency_id = $originalModel->currency_id;
            $nextMonthPayment->month = $nextMonth;
            $nextMonthPayment->type = $originalModel->type;
            $nextMonthPayment->cashbox_id = $originalModel->cashbox_id;
            $nextMonthPayment->description = $originalModel->description . ' (перенос с ' . $originalModel->month . ')';
            $nextMonthPayment->debt_deduction_amount = 0;
            $nextMonthPayment->net_amount = $amountForNextMonth;
            $nextMonthPayment->created_at = date('Y-m-d H:i:s');

            if (!$nextMonthPayment->save()) {
                throw new \Exception('Не удалось создать запись для месяца ' . $nextMonth . ': ' . json_encode($nextMonthPayment->getErrors(), JSON_UNESCAPED_UNICODE));
            }

            // Создаем expense_history для новой записи
            $this->createExpenseHistoryForSalary($nextMonthPayment);

            $remainingAmount -= $amountForNextMonth;
            $currentMonth = $nextMonth;
        }
    }

    /**
     * Создание записи в expense_history для зарплат и авансов
     */
    private function createExpenseHistoryForSalary($model)
    {
        $expenseTypeId = null;
        if ($model->type == EmployeeFinances::TYPE_ADVANCE) {
            $expenseTypeId = Expenses::find()
                ->select(['id'])
                ->where(['name' => 'Аванс', 'deleted_at' => null])
                ->scalar();
        } else if ($model->type == EmployeeFinances::TYPE_SALARY) {
            $expenseTypeId = Expenses::find()
                ->select(['id'])
                ->where(['name' => 'Маош', 'deleted_at' => null])
                ->scalar();
        }

        if ($expenseTypeId) {
            $this->createExpenseHistory(
                $model->cashbox_id,
                $model->currency_id,
                $expenseTypeId,
                $model->net_amount,
                $model->id,
                'Ходим учун тўлов'
            );
        }
    }

    private function createPaymentRecord($model, $amount, $month, $employeeSalary)
    {
        if ($model->type == EmployeeFinances::TYPE_BONUS) {
            $payment = new EmployeeFinances();
            $payment->currency_id = $model->currency_id;
            $payment->employee_id = $model->employee_id;
            $payment->user_id = Yii::$app->user->id;
            $payment->amount = $amount;
            $payment->month = $month;
            $payment->cashbox_id = $model->cashbox_id;
            $payment->description = $model->description;
            $payment->type = EmployeeFinances::TYPE_BONUS;
            $payment->created_at = date('Y-m-d H:i:s');

            // Устанавливаем обязательные поля для новых записей
            $payment->debt_deduction_amount = 0;
            $payment->net_amount = $payment->amount;

            if (!$payment->save()) {
                throw new \Exception('Тўловни сақлашда хатолик: ' . json_encode($payment->getErrors(), JSON_UNESCAPED_UNICODE));
            }

            // Создаем запись в expense_history для бонуса
            $expenseTypeId = Expenses::find()
                ->select(['id'])
                ->where(['name' => 'Бонус', 'deleted_at' => null])
                ->scalar();

            $this->createExpenseHistory(
                $payment->cashbox_id,
                $payment->currency_id,
                $expenseTypeId,
                $payment->amount,
                $payment->id,
                'Ходим учун тўлов'
            );

            return true;
        }

        $totalPaid = EmployeeFinances::find()
            ->where([
                'employee_id' => $model->employee_id,
                'month' => $month,
                'deleted_at' => null,
            ])
            ->andWhere(['<>', 'type', EmployeeFinances::TYPE_BONUS])
            ->sum('amount');

        $remaining = $employeeSalary->amount - ($totalPaid ?: 0);

        if ($remaining <= 0) {
            $nextMonth = date('Y-m', strtotime($month . '-01 +1 month'));
            return $this->createPaymentRecord($model, $amount, $nextMonth, $employeeSalary);
        }

        if ($amount > $remaining) {
            $currentMonth = new EmployeeFinances();
            $currentMonth->employee_id = $model->employee_id;
            $currentMonth->user_id = Yii::$app->user->id;
            $currentMonth->amount = $remaining;
            $currentMonth->currency_id = $model->currency_id;
            $currentMonth->month = $month;
            $currentMonth->type = $model->type;
            $currentMonth->created_at = date('Y-m-d H:i:s');
            $currentMonth->cashbox_id = $model->cashbox_id;
            $currentMonth->description = $model->description;

            // Устанавливаем обязательные поля для новых записей
            $currentMonth->debt_deduction_amount = 0;
            $currentMonth->net_amount = $currentMonth->amount;

            if (!$currentMonth->save()) {
                throw new \Exception('Тўловни сақлашда хатолик: ' . json_encode($currentMonth->getErrors(), JSON_UNESCAPED_UNICODE));
            }

            // Создаем запись в expense_history для текущего месяца
            if($model->type == EmployeeFinances::TYPE_ADVANCE) {
                $expenseTypeId = Expenses::find()
                    ->select(['id'])
                    ->where(['name' => 'Маош', 'deleted_at' => null])
                    ->scalar();
            } else if($model->type == EmployeeFinances::TYPE_SALARY) {
                $expenseTypeId = Expenses::find()
                    ->select(['id'])
                    ->where(['name' => 'Аванс', 'deleted_at' => null])
                    ->scalar();
            } else {
                $expenseTypeId = Expenses::find()
                    ->select(['id'])
                    ->where(['name' => 'Бонус', 'deleted_at' => null])
                    ->scalar();
            }

            $this->createExpenseHistory(
                $currentMonth->cashbox_id,
                $currentMonth->currency_id,
                $expenseTypeId,
                $currentMonth->amount,
                $currentMonth->id,
                'Ходим учун тўлов'
            );

            $nextMonth = date('Y-m', strtotime($month . '-01 +1 month'));
            return $this->createPaymentRecord($model, $amount - $remaining, $nextMonth, $employeeSalary);
        }

        // Если сумма помещается в текущий месяц
        $payment = new EmployeeFinances();
        $payment->currency_id = $model->currency_id;
        $payment->type = $model->type;
        $payment->attributes = $model->attributes;
        $payment->amount = $amount;
        $payment->month = $month;
        $payment->created_at = date('Y-m-d H:i:s');
        $payment->cashbox_id = $model->cashbox_id;
        $payment->description = $model->description;
        $payment->user_id = Yii::$app->user->id;

        // Устанавливаем обязательные поля для новых записей
        $payment->debt_deduction_amount = 0;
        $payment->net_amount = $payment->amount;

        if (!$payment->save()) {
            throw new \Exception('Тўловни сақлашда хатолик: ' . json_encode($payment->getErrors(), JSON_UNESCAPED_UNICODE));
        }

        // Создаем запись в expense_history
        if($model->type == EmployeeFinances::TYPE_ADVANCE) {
            $expenseTypeId = Expenses::find()
                ->select(['id'])
                ->where(['name' => 'Аванс', 'deleted_at' => null])
                ->scalar();
        } else if($model->type == EmployeeFinances::TYPE_SALARY) {
            $expenseTypeId = Expenses::find()
                ->select(['id'])
                ->where(['name' => 'Маош', 'deleted_at' => null])
                ->scalar();
        } else if($model->type == EmployeeFinances::TYPE_BONUS) {
            $expenseTypeId = Expenses::find()
                ->select(['id'])
                ->where(['name' => 'Бонус', 'deleted_at' => null])
                ->scalar();
        }

        $this->createExpenseHistory(
            $payment->cashbox_id,
            $payment->currency_id,
            $expenseTypeId,
            $payment->amount,
            $payment->id,
            'Ходим учун тўлов'
        );

        return true;
    }

    private function createCashboxBalanceRecords($cashboxBalanceId, $cashboxId, $currencyId, $amount, $description)
    {
        $cashboxBalanceHistory = new CashboxBalanceHistory();
        $cashboxBalanceHistory->cashbox_balance_id = $cashboxBalanceId;
        $cashboxBalanceHistory->cashbox_id = $cashboxId;
        $cashboxBalanceHistory->currency_id = $currencyId;
        $cashboxBalanceHistory->user_id = Yii::$app->user->id;
        $cashboxBalanceHistory->sum = $amount;
        $cashboxBalanceHistory->type = CashboxBalanceHistory::TYPE_EXPENSES; // Тип "расход"
        $cashboxBalanceHistory->description = $description;
        $cashboxBalanceHistory->module_type = CashboxBalanceHistory::MODULE_USER_TYPE_TRANSFER;
        $cashboxBalanceHistory->created_at = date('Y-m-d H:i:s');

        if (!$cashboxBalanceHistory->save()) {
            throw new \Exception('Не удалось сохранить историю баланса кассы: ' . json_encode($cashboxBalanceHistory->getErrors(), JSON_UNESCAPED_UNICODE));
        }

        // Создаем запись расхода
        $cashboxBalanceInouts = new CashboxBalanceInouts();
        $cashboxBalanceInouts->cashbox_id = $cashboxId;
        $cashboxBalanceInouts->currency_id = $currencyId;
        $cashboxBalanceInouts->sum = $amount;
        $cashboxBalanceInouts->date = date('Y-m-d');

        if (!$cashboxBalanceInouts->save()) {
            throw new \Exception('Не удалось сохранить расход: ' . json_encode($cashboxBalanceInouts->getErrors(), JSON_UNESCAPED_UNICODE));
        }
    }

    private function createExpenseHistory($cashboxId, $currencyId, $expenseId, $amount, $paymentId, $description)
    {
        $expenseHistory = new ExpenseHistory();
        $expenseHistory->employee_finance_id = $paymentId;
        $expenseHistory->cashbox_id = $cashboxId;
        $expenseHistory->currency_id = $currencyId;
        $expenseHistory->expense_id = $expenseId;
        $expenseHistory->sum = $amount;
        $expenseHistory->user_id = Yii::$app->user->id;
        $expenseHistory->created_at = date('Y-m-d H:i:s');
        $expenseHistory->description = $description;

        // Добавляем информацию о погашении долгов, если есть
        $employeeFinance = EmployeeFinances::findOne($paymentId);
        if ($employeeFinance && $employeeFinance->debt_deduction_amount > 0) {
            $expenseHistory->debt_repayment_amount = $employeeFinance->debt_deduction_amount;
        }

        if (!$expenseHistory->save()) {
            throw new \Exception('Не удалось сохранить запись в expense_history: ' . json_encode($expenseHistory->getErrors(), JSON_UNESCAPED_UNICODE));
        }
    }

    public function actionDelete()
    {
        Yii::$app->response->format = Response::FORMAT_JSON;

        if(Yii::$app->request->isGet)
        {
            $id = Yii::$app->request->get('id');
            $model = EmployeeFinances::findOne($id);
            if(!$model)
            {
                return [
                    'status' => 'error',
                    'message' => 'Запись не найдена',
                ];
            }
            return [
                'status' => 'success',
                'content' => $this->renderPartial('delete', ['model' => $model]),
            ];
        }

        if (!Yii::$app->request->isPost) {
            return [
                'status' => 'error',
                'message' => 'Неверный метод запроса',
            ];
        }

        $data = Yii::$app->request->post();
        $model = EmployeeFinances::findOne($data['EmployeeFinances']['id']);

        if (!$model) {
            return [
                'status' => 'error',
                'message' => 'Запись не найдена',
            ];
        }

        $transaction = Yii::$app->db->beginTransaction();
        try {
            $employeeSalary = EmployeeSalary::find()
                ->where([
                    'employee_id' => $model->employee_id,
                    'end_date' => '9999-12-31',
                    'deleted_at' => null
                ])
                ->one();

            // Проверяем: если это не бонус и нет зарплаты - выдаем ошибку
            if ($model->type != EmployeeFinances::TYPE_BONUS && !$employeeSalary) {
                throw new \Exception('Ходимнинг актив маоши топилмади!');
            }

            // Отменяем погашения долгов, если они есть
            if ($model->debt_deduction_amount > 0) {
                $this->cancelDebtRepayments($model->id);
            }

            $this->deleteExpenseHistory($model);
            $this->returnDeletedAmountToCashbox($model);

            $model->deleted_at = date('Y-m-d H:i:s');
            if (!$model->save()) {
                throw new \Exception('Ошибка при удалении записи');
            }

            // Перерасчет только если это не бонус и есть зарплата
            if ($model->type != EmployeeFinances::TYPE_BONUS && $employeeSalary) {
                $this->recalculatePaymentsAfterDelete($model->employee_id, $model->month, $employeeSalary, $model);
            }

            // Логируем удаление выплаты сотруднику
            \app\common\models\ActionLog::logAction(
                Yii::$app->user->id,
                \app\common\models\ActionLog::TYPE_EMPLOYEE_PAYMENT,
                json_encode([
                    'id' => $model->id,
                    'employee_id' => $model->employee_id,
                    'amount' => $model->amount,
                    'currency_id' => $model->currency_id,
                    'type' => $model->type,
                    'month' => $model->month,
                    'cashbox_id' => $model->cashbox_id,
                    'description' => $model->description
                ]),
                null
            );

            $transaction->commit();
            return [
                'status' => 'success',
                'message' => 'Тўлов муваффақиятли ўчирилди',
            ];

        } catch (\Exception $e) {
            $transaction->rollBack();
            return [
                'status' => 'error',
                'message' => $e->getMessage(),
            ];
        }
    }

    private function deleteExpenseHistory($model)
    {
        $expenseHistory = ExpenseHistory::find()
            ->where([
                'employee_finance_id' => $model->id,
                'deleted_at' => null
            ])
            ->orderBy(['created_at' => SORT_ASC])
            ->one();

        if ($expenseHistory) {
            $expenseHistory->deleted_at = date('Y-m-d H:i:s');
            if (!$expenseHistory->save()) {
                throw new \Exception('Не удалось обновить запись в expense_history');
            }
        }
    }

    private function returnDeletedAmountToCashbox($model)
    {
        $this->updateCashboxBalance($model);
    }

    private function updateCashboxBalance($model)
    {
        $cashboxBalance = CashboxBalance::find()
            ->where(['cashbox_id' => $model->cashbox_id, 'currency_id' => $model->currency_id])
            ->one();

        if (!$cashboxBalance) {
            throw new \Exception('Баланс кассы не найден');
        }

        $cashboxBalance->sum += $model->net_amount;
        if (!$cashboxBalance->save()) {
            throw new \Exception('Не удалось обновить баланс кассы');
        }

        $balanceHistory = CashboxBalanceHistory::find()
            ->where(['cashbox_balance_id' => $cashboxBalance->id, 'deleted_at' => null])
            ->orderBy(['created_at' => SORT_ASC])
            ->one();

        if ($balanceHistory) {
            $balanceHistory->deleted_at = date('Y-m-d H:i:s');
            if (!$balanceHistory->save()) {
                throw new \Exception('Не удалось обновить историю баланса кассы');
            }
        }
    }

    private function recalculatePaymentsAfterDelete($employeeId, $startMonth, $employeeSalary, $model)
    {
        $allPayments = EmployeeFinances::find()
            ->where([
                'employee_id' => $employeeId,
                'deleted_at' => null
            ])
            ->andWhere(['>=', 'month', $startMonth])
            ->andWhere(['<>', 'type', EmployeeFinances::TYPE_BONUS])
            ->orderBy(['month' => SORT_ASC, 'id' => SORT_ASC])
            ->all();

        $totalAmountToDistribute = 0;
        foreach ($allPayments as $payment) {
            $totalAmountToDistribute += $payment->amount;

            $payment->deleted_at = date('Y-m-d H:i:s');
            if (!$payment->save()) {
                throw new \Exception('Ошибка при удалении платежа');
            }
        }

        if ($totalAmountToDistribute <= 0) {
            return;
        }

        $currentMonth = $startMonth;
        $remainingAmount = $totalAmountToDistribute;

        while ($remainingAmount > 0) {
            $payment = new EmployeeFinances();
            $payment->employee_id = $employeeId;
            $payment->month = $currentMonth;
            $payment->currency_id = $model->currency_id;
            $payment->cashbox_id = $model->cashbox_id;
            $payment->type = $model->type;
            $payment->user_id = Yii::$app->user->id;
            $payment->created_at = date('Y-m-d H:i:s');

            $amountForMonth = min($remainingAmount, $employeeSalary->amount);
            $payment->amount = $amountForMonth;
            $payment->description = 'Ходимга тулов';

            // Устанавливаем обязательные поля для новых записей
            $payment->debt_deduction_amount = 0;
            $payment->net_amount = $payment->amount;

            if (!$payment->save()) {
                throw new \Exception('Ошибка при создании нового платежа: ' . json_encode($payment->getErrors(), JSON_UNESCAPED_UNICODE));
            }

            $remainingAmount -= $amountForMonth;
            if ($remainingAmount > 0) {
                $currentMonth = date('Y-m', strtotime($currentMonth . '-01 +1 month'));
            }
        }
    }

    /**
     * Обработка погашения долгов при выплате зарплаты
     */
    private function processDebtRepayments($employeeId, $currencyId, $deductionAmount, $employeeFinanceId)
    {
        // Получаем активные долги сотрудника в указанной валюте
        $activeDebts = EmployeeDebt::getActiveDebts($employeeId, $currencyId);

        if (empty($activeDebts)) {
            throw new \Exception('У сотрудника нет активных долгов в указанной валюте');
        }

        $remainingDeduction = $deductionAmount;
        $totalDebtAmount = 0;

        // Проверяем, что общая сумма долгов покрывает удержание
        foreach ($activeDebts as $debt) {
            $totalDebtAmount += $debt->remaining_amount;
        }

        if ($totalDebtAmount < $deductionAmount) {
            throw new \Exception('Сумма удержания превышает общий остаток долгов сотрудника');
        }

        // Погашаем долги по принципу FIFO (первый пришел - первый ушел)
        foreach ($activeDebts as $debt) {
            if ($remainingDeduction <= 0) {
                break;
            }

            $repaymentAmount = min($remainingDeduction, $debt->remaining_amount);

            // Создаем запись о погашении
            $debt->repay($repaymentAmount, $employeeFinanceId, 'Удержание с зарплаты');

            $remainingDeduction -= $repaymentAmount;

            // Логируем погашение долга
            \app\common\models\ActionLog::logAction(
                Yii::$app->user->id,
                \app\common\models\ActionLog::TYPE_DEBT_REPAYMENT,
                null,
                json_encode([
                    'debt_id' => $debt->id,
                    'employee_id' => $employeeId,
                    'employee_finance_id' => $employeeFinanceId,
                    'repayment_amount' => $repaymentAmount,
                    'currency_id' => $currencyId,
                    'remaining_debt' => $debt->remaining_amount,
                    'created_at' => date('Y-m-d H:i:s')
                ])
            );
        }

        if ($remainingDeduction > 0) {
            throw new \Exception('Не удалось полностью распределить сумму удержания по долгам');
        }
    }

    /**
     * Отмена погашений долгов при удалении выплаты
     */
    private function cancelDebtRepayments($employeeFinanceId)
    {
        $repayments = EmployeeDebtRepayment::getByEmployeeFinance($employeeFinanceId);

        foreach ($repayments as $repayment) {
            $repayment->cancel();

            // Логируем отмену погашения
            \app\common\models\ActionLog::logAction(
                Yii::$app->user->id,
                \app\common\models\ActionLog::TYPE_DEBT_REPAYMENT,
                json_encode([
                    'action' => 'cancel',
                    'repayment_id' => $repayment->id,
                    'debt_id' => $repayment->debt_id,
                    'employee_finance_id' => $employeeFinanceId,
                    'amount' => $repayment->amount,
                    'currency_id' => $repayment->currency_id
                ]),
                null
            );
        }
    }

}