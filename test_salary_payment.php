<?php
/**
 * Тестовый скрипт для проверки исправления проблемы с оплатой зарплаты
 * Проверяет, что при разделении платежа на месяцы из кассы вычитается полная сумма
 */

// Инициализация Yii2
require __DIR__ . '/vendor/autoload.php';
require __DIR__ . '/vendor/yiisoft/yii2/Yii.php';

$config = require __DIR__ . '/config/console.php';
$application = new yii\console\Application($config);

echo "=== ТЕСТ ИСПРАВЛЕНИЯ ПРОБЛЕМЫ С ОПЛАТОЙ ЗАРПЛАТЫ ===\n\n";

try {
    // Начинаем транзакцию для возможности отката
    $transaction = Yii::$app->db->beginTransaction();
    
    echo "1. Поиск тестовых данных...\n";
    
    // Находим существующего сотрудника с зарплатой
    $employee = \app\common\models\Employee::find()
        ->where(['deleted_at' => null])
        ->one();
    
    if (!$employee) {
        throw new Exception('Не найден ни один сотрудник');
    }
    
    echo "   Найден сотрудник: ID={$employee->id}, {$employee->full_name}\n";
    
    // Проверяем или создаем зарплату для сотрудника
    $employeeSalary = \app\common\models\EmployeeSalary::find()
        ->where([
            'employee_id' => $employee->id,
            'end_date' => '9999-12-31',
            'deleted_at' => null
        ])
        ->one();
    
    if (!$employeeSalary) {
        echo "   Создаем тестовую зарплату для сотрудника...\n";
        $employeeSalary = new \app\common\models\EmployeeSalary();
        $employeeSalary->employee_id = $employee->id;
        $employeeSalary->user_id = 1; // Предполагаем, что пользователь с ID 1 существует
        $employeeSalary->amount = 1000000; // 1,000,000 сум в месяц
        $employeeSalary->start_date = date('Y-m-d');
        $employeeSalary->end_date = '9999-12-31';
        $employeeSalary->created_at = date('Y-m-d H:i:s');
        
        if (!$employeeSalary->save()) {
            throw new Exception('Не удалось создать зарплату: ' . json_encode($employeeSalary->getErrors()));
        }
    }
    
    echo "   Зарплата сотрудника: {$employeeSalary->amount} сум/месяц\n";
    
    // Находим кассу
    $cashbox = \app\common\models\Cashbox::find()
        ->where(['deleted_at' => null])
        ->one();
    
    if (!$cashbox) {
        throw new Exception('Не найдена ни одна касса');
    }
    
    echo "   Найдена касса: ID={$cashbox->id}, {$cashbox->name}\n";
    
    // Находим валюту (сум)
    $currency = \app\common\models\Currency::find()
        ->where(['name' => 'Сўм'])
        ->one();
    
    if (!$currency) {
        $currency = \app\common\models\Currency::find()->one();
    }
    
    if (!$currency) {
        throw new Exception('Не найдена ни одна валюта');
    }
    
    echo "   Найдена валюта: ID={$currency->id}, {$currency->name}\n";
    
    // Проверяем или создаем баланс кассы
    $cashboxBalance = \app\common\models\CashboxBalance::find()
        ->where([
            'cashbox_id' => $cashbox->id,
            'currency_id' => $currency->id,
            'deleted_at' => null
        ])
        ->one();
    
    if (!$cashboxBalance) {
        echo "   Создаем баланс кассы...\n";
        $cashboxBalance = new \app\common\models\CashboxBalance();
        $cashboxBalance->cashbox_id = $cashbox->id;
        $cashboxBalance->currency_id = $currency->id;
        $cashboxBalance->sum = 5000000; // 5,000,000 сум
        $cashboxBalance->created_at = date('Y-m-d H:i:s');
        
        if (!$cashboxBalance->save()) {
            throw new Exception('Не удалось создать баланс кассы: ' . json_encode($cashboxBalance->getErrors()));
        }
    }
    
    $initialCashboxBalance = $cashboxBalance->sum;
    echo "   Начальный баланс кассы: {$initialCashboxBalance} {$currency->name}\n\n";
    
    echo "2. Создание тестового платежа...\n";
    
    // Создаем платеж, который должен разделиться на месяцы
    // Сумма: 1,500,000 (больше месячной зарплаты 1,000,000)
    $testAmount = 1500000;
    echo "   Сумма платежа: {$testAmount} {$currency->name}\n";
    echo "   Ожидается разделение: 1,000,000 (текущий месяц) + 500,000 (следующий месяц)\n";
    
    $model = new \app\common\models\EmployeeFinances();
    $model->employee_id = $employee->id;
    $model->user_id = 1;
    $model->amount = $testAmount;
    $model->month = date('Y-m');
    $model->type = \app\common\models\EmployeeFinances::TYPE_SALARY;
    $model->cashbox_id = $cashbox->id;
    $model->currency_id = $currency->id;
    $model->description = 'ТЕСТОВЫЙ ПЛАТЕЖ - проверка исправления';
    $model->debt_deduction_amount = 0;
    $model->net_amount = $testAmount;
    $model->created_at = date('Y-m-d H:i:s');
    
    echo "   Создание платежа напрямую...\n";

    // Имитируем пользователя
    Yii::$app->user->setIdentity(new \app\common\models\User(['id' => 1]));

    // Сохраняем платеж
    if (!$model->save()) {
        throw new Exception('Не удалось сохранить основную запись: ' . json_encode($model->getErrors()));
    }

    echo "   Основная запись создана с ID: {$model->id}\n";

    // Сохраняем оригинальную сумму ДО обработки разделения на месяцы
    $originalNetAmount = $model->net_amount;

    // Проверяем, нужно ли разделение на месяцы
    $totalPaid = \app\common\models\EmployeeFinances::find()
        ->where([
            'employee_id' => $model->employee_id,
            'month' => $model->month,
            'deleted_at' => null,
        ])
        ->andWhere(['<>', 'type', \app\common\models\EmployeeFinances::TYPE_BONUS])
        ->andWhere(['<>', 'id', $model->id])
        ->sum('amount');

    $remaining = $employeeSalary->amount - ($totalPaid ?: 0);

    echo "   Уже выплачено в этом месяце: " . ($totalPaid ?: 0) . "\n";
    echo "   Остается в месяце: {$remaining}\n";
    echo "   Сумма платежа: {$model->amount}\n";

    if ($model->amount > $remaining) {
        echo "   Требуется разделение платежа на месяцы\n";

        // Обновляем основную запись для текущего месяца
        $model->amount = $remaining;
        $model->net_amount = $remaining;
        if (!$model->save()) {
            throw new Exception('Не удалось обновить основную запись');
        }

        // Создаем запись для следующего месяца
        $nextMonth = date('Y-m', strtotime($model->month . '-01 +1 month'));
        $remainingAmount = $testAmount - $remaining;

        $nextMonthPayment = new \app\common\models\EmployeeFinances();
        $nextMonthPayment->employee_id = $model->employee_id;
        $nextMonthPayment->user_id = $model->user_id;
        $nextMonthPayment->amount = $remainingAmount;
        $nextMonthPayment->currency_id = $model->currency_id;
        $nextMonthPayment->month = $nextMonth;
        $nextMonthPayment->type = $model->type;
        $nextMonthPayment->cashbox_id = $model->cashbox_id;
        $nextMonthPayment->description = $model->description . ' (перенос с ' . $model->month . ')';
        $nextMonthPayment->debt_deduction_amount = 0;
        $nextMonthPayment->net_amount = $remainingAmount;
        $nextMonthPayment->created_at = date('Y-m-d H:i:s');

        if (!$nextMonthPayment->save()) {
            throw new Exception('Не удалось создать запись для следующего месяца');
        }

        echo "   Создана дополнительная запись с ID: {$nextMonthPayment->id} на сумму {$remainingAmount}\n";
    }

    // Обновляем баланс кассы (используем оригинальную сумму)
    $cashboxBalance->sum -= $originalNetAmount;
    if (!$cashboxBalance->save()) {
        throw new Exception('Не удалось обновить баланс кассы');
    }

    echo "   Из кассы вычтено: {$originalNetAmount}\n";
    
    echo "   ✅ Платеж создан успешно\n\n";
    
    echo "3. Проверка результатов...\n";
    
    // Проверяем записи в employee_finances
    $financeRecords = \app\common\models\EmployeeFinances::find()
        ->where([
            'employee_id' => $employee->id,
            'description' => 'ТЕСТОВЫЙ ПЛАТЕЖ - проверка исправления',
            'deleted_at' => null
        ])
        ->orWhere(['like', 'description', 'ТЕСТОВЫЙ ПЛАТЕЖ - проверка исправления (перенос с'])
        ->orderBy(['created_at' => SORT_ASC])
        ->all();
    
    echo "   Записи в employee_finances:\n";
    $totalFinanceAmount = 0;
    foreach ($financeRecords as $record) {
        echo "     ID: {$record->id}, Сумма: {$record->amount}, Месяц: {$record->month}\n";
        $totalFinanceAmount += $record->amount;
    }
    echo "   Общая сумма в employee_finances: {$totalFinanceAmount}\n\n";
    
    // Проверяем записи в expense_history
    $expenseRecords = \app\common\models\ExpenseHistory::find()
        ->where(['employee_finance_id' => array_map(function($r) { return $r->id; }, $financeRecords)])
        ->andWhere(['deleted_at' => null])
        ->all();
    
    echo "   Записи в expense_history:\n";
    $totalExpenseAmount = 0;
    foreach ($expenseRecords as $record) {
        echo "     ID: {$record->id}, Сумма: {$record->sum}, Employee Finance ID: {$record->employee_finance_id}\n";
        $totalExpenseAmount += $record->sum;
    }
    echo "   Общая сумма в expense_history: {$totalExpenseAmount}\n\n";
    
    // Проверяем изменение баланса кассы
    $cashboxBalance->refresh();
    $finalCashboxBalance = $cashboxBalance->sum;
    $cashboxDifference = $initialCashboxBalance - $finalCashboxBalance;
    
    echo "   Баланс кассы:\n";
    echo "     Начальный: {$initialCashboxBalance}\n";
    echo "     Конечный: {$finalCashboxBalance}\n";
    echo "     Разница: {$cashboxDifference}\n\n";
    
    // Проверяем записи в cashbox_balance_history
    $balanceHistoryRecords = \app\common\models\CashboxBalanceHistory::find()
        ->where([
            'cashbox_id' => $cashbox->id,
            'currency_id' => $currency->id,
            'description' => 'Ходим учун тўлов',
            'deleted_at' => null
        ])
        ->andWhere(['>=', 'created_at', date('Y-m-d H:i:s', time() - 60)]) // За последнюю минуту
        ->all();
    
    echo "   Записи в cashbox_balance_history:\n";
    $totalHistoryAmount = 0;
    foreach ($balanceHistoryRecords as $record) {
        echo "     ID: {$record->id}, Сумма: {$record->sum}\n";
        $totalHistoryAmount += $record->sum;
    }
    echo "   Общая сумма в cashbox_balance_history: {$totalHistoryAmount}\n\n";
    
    // Проверяем записи в cashbox_balance_inouts
    $inoutRecords = \app\common\models\CashboxBalanceInouts::find()
        ->where([
            'cashbox_id' => $cashbox->id,
            'currency_id' => $currency->id,
            'date' => date('Y-m-d')
        ])
        ->andWhere(['>=', 'sum', 500000]) // Ищем записи с суммами от нашего теста
        ->orderBy(['id' => SORT_DESC])
        ->limit(5)
        ->all();
    
    echo "   Записи в cashbox_balance_inouts (последние):\n";
    $totalInoutAmount = 0;
    foreach ($inoutRecords as $record) {
        echo "     ID: {$record->id}, Сумма: {$record->sum}, Дата: {$record->date}\n";
        $totalInoutAmount += $record->sum;
    }
    
    echo "\n4. РЕЗУЛЬТАТ ТЕСТА:\n";
    
    $success = true;
    $errors = [];
    
    // Проверка 1: Общая сумма в employee_finances должна равняться тестовой сумме
    if ($totalFinanceAmount != $testAmount) {
        $success = false;
        $errors[] = "❌ Сумма в employee_finances ({$totalFinanceAmount}) не равна тестовой сумме ({$testAmount})";
    } else {
        echo "✅ Сумма в employee_finances корректна: {$totalFinanceAmount}\n";
    }
    
    // Проверка 2: Из кассы должна быть вычтена полная сумма
    if ($cashboxDifference != $testAmount) {
        $success = false;
        $errors[] = "❌ Из кассы вычтено {$cashboxDifference}, а должно быть {$testAmount}";
    } else {
        echo "✅ Из кассы вычтена корректная сумма: {$cashboxDifference}\n";
    }
    
    // Проверка 3: Должно быть создано минимум 2 записи в employee_finances (разделение)
    if (count($financeRecords) < 2) {
        $success = false;
        $errors[] = "❌ Создано только " . count($financeRecords) . " записей в employee_finances, ожидалось минимум 2";
    } else {
        echo "✅ Платеж корректно разделен на " . count($financeRecords) . " записей\n";
    }
    
    if ($success) {
        echo "\n🎉 ТЕСТ ПРОЙДЕН УСПЕШНО! Проблема исправлена.\n";
        echo "   Из кассы корректно вычитается полная сумма платежа при разделении на месяцы.\n";
    } else {
        echo "\n❌ ТЕСТ НЕ ПРОЙДЕН! Обнаружены проблемы:\n";
        foreach ($errors as $error) {
            echo "   {$error}\n";
        }
    }
    
    // Откатываем транзакцию, чтобы не засорять базу тестовыми данными
    $transaction->rollBack();
    echo "\n📝 Тестовые данные удалены (транзакция откачена)\n";
    
} catch (Exception $e) {
    if (isset($transaction)) {
        $transaction->rollBack();
    }
    echo "❌ ОШИБКА: " . $e->getMessage() . "\n";
    echo "Стек вызовов:\n" . $e->getTraceAsString() . "\n";
}

echo "\n=== КОНЕЦ ТЕСТА ===\n";
